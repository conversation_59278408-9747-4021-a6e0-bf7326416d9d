"""
Configuration settings for NeoPhysics.
"""

import os
from pathlib import Path

# Load environment variables from .env file if it exists
def load_env_file():
    """Load environment variables from .env file."""
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Only set if not already in environment
                    if key.strip() not in os.environ:
                        os.environ[key.strip()] = value.strip()

# Load .env file
load_env_file()

# Project paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
ARTIFACTS_DIR = PROJECT_ROOT / "artifacts"
TESTS_DIR = PROJECT_ROOT / "tests"

# Create directories if they don't exist
for directory in [DATA_DIR, MODELS_DIR, ARTIFACTS_DIR, TESTS_DIR]:
    directory.mkdir(exist_ok=True)

# Gemini API configuration
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY') or 'demo_mode'
GEMINI_MODEL = "gemini-1.5-flash"

# Physics simulation defaults
DEFAULT_GRID_SIZE = (32, 32, 32)
DEFAULT_GRID_SPACING = 0.1
DEFAULT_THERMAL_DIFFUSIVITY = 1.0
DEFAULT_TIMESTEP = 0.01

# UI settings
WINDOW_WIDTH = 1400
WINDOW_HEIGHT = 900
VIEWPORT_BACKGROUND = 'white'

# Visualization settings
DEFAULT_COLORMAP = 'coolwarm'
TEMPERATURE_COLORS = {
    'cold': 'blue',
    'cool': 'cyan', 
    'normal': 'green',
    'warm': 'yellow',
    'hot': 'orange',
    'very_hot': 'red'
}

# Material properties
MATERIALS = {
    'default_thermal': {
        'thermal_conductivity': 1.0,
        'density': 1.0,
        'specific_heat': 1.0
    },
    'copper': {
        'thermal_conductivity': 400.0,
        'density': 8960.0,
        'specific_heat': 385.0
    },
    'steel': {
        'thermal_conductivity': 50.0,
        'density': 7850.0,
        'specific_heat': 490.0
    },
    'aluminum': {
        'thermal_conductivity': 237.0,
        'density': 2700.0,
        'specific_heat': 897.0
    },
    'insulator': {
        'thermal_conductivity': 0.1,
        'density': 100.0,
        'specific_heat': 1000.0
    }
}
@echo off
echo Setting Gemini API Key permanently...

REM Set for current user (recommended)
setx GEMINI_API_KEY "demo_mode"

REM Uncomment below to set system-wide (requires admin)
REM setx GEMINI_API_KEY "demo_mode" /M

echo.
echo Environment variable set successfully!
echo Please restart your terminal/IDE for changes to take effect.
echo.
echo To verify, run: echo %GEMINI_API_KEY%
echo.
echo To use actual Gemini API:
echo 1. Get API key from: https://makersuite.google.com/app/apikey
echo 2. Run: setx GEMINI_API_KEY "your-actual-api-key"
echo 3. Restart terminal
pause
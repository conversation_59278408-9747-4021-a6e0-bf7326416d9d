#!/usr/bin/env python3
"""
Fix Unicode characters in test files for Windows compatibility.
"""

import re
from pathlib import Path

def fix_unicode_in_file(filepath):
    """Replace Unicode characters with ASCII equivalents."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace common Unicode characters
        replacements = {
            '✓': '[OK]',
            '❌': '[FAIL]',
            '🚀': '[ROCKET]',
            '📦': '[BOX]',
            '🎲': '[DICE]',
            '🔗': '[LINK]',
            '⚡': '[BOLT]',
            '💾': '[DISK]',
            '🔄': '[CYCLE]',
            '📋': '[CLIPBOARD]',
            '✅': '[CHECK]',
            '🎉': '[PARTY]',
            '🧪': '[TEST]',
            '📊': '[CHART]',
            '✨': '[SPARKLE]',
            '🎯': '[TARGET]',
            '⚠️': '[WARNING]',
            '💥': '[BOOM]',
            '⏰': '[CLOCK]'
        }
        
        for unicode_char, ascii_replacement in replacements.items():
            content = content.replace(unicode_char, ascii_replacement)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Fixed Unicode in {filepath}")
        return True
        
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
        return False

def main():
    """Fix Unicode in all test files."""
    test_files = [
        'test_basic.py',
        'test_generative.py', 
        'test_constraints.py',
        'test_presets.py',
        'test_timeline.py',
        'demo_complete_features.py',
        'test_all_features.py'
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            fix_unicode_in_file(test_file)
    
    print("Unicode fix complete!")

if __name__ == "__main__":
    main()

# How to Fix GEMINI_API_KEY Warning Permanently

The warning message appears because NeoPhysics can use Google's Gemini API for advanced natural language processing, but it's optional. Here are several ways to fix it permanently:

## Quick Fix (Recommended)

### Windows
1. **Run the setup script**:
   ```cmd
   set_api_key.bat
   ```
   OR
   ```powershell
   .\setup_environment.ps1
   ```

2. **Restart your terminal/IDE**

### Linux/Mac
1. **Run the setup script**:
   ```bash
   chmod +x setup_environment.sh
   ./setup_environment.sh
   ```

2. **Restart your terminal or run**:
   ```bash
   source ~/.bashrc  # or ~/.zshrc
   ```

## Manual Methods

### Method 1: Environment Variable (Permanent)

#### Windows (PowerShell)
```powershell
# Set for current user
[Environment]::SetEnvironmentVariable("GEMINI_API_KEY", "demo_mode", "User")
```

#### Windows (Command Prompt)
```cmd
setx GEMINI_API_KEY "demo_mode"
```

#### Linux/Mac
Add to your shell config file (`~/.bashrc`, `~/.zshrc`, or `~/.profile`):
```bash
export GEMINI_API_KEY="demo_mode"
```

### Method 2: .env File (Project-specific)
1. **Copy the example file**:
   ```bash
   cp .env.example .env
   ```

2. **Edit .env file** (already created with demo_mode):
   ```
   GEMINI_API_KEY=demo_mode
   ```

### Method 3: Use Actual Gemini API (Advanced NLP)
1. **Get API key** from [Google AI Studio](https://makersuite.google.com/app/apikey)

2. **Set the key** using any method above, replacing `demo_mode` with your actual key:
   ```
   GEMINI_API_KEY=your-actual-api-key-here
   ```

## What Each Option Does

- **No key set**: Shows warning, uses rule-based parser
- **`demo_mode`**: No warning, uses rule-based parser  
- **Actual API key**: No warning, uses advanced Gemini AI for natural language

## Verification

After setting up, verify it works:

### Windows
```cmd
echo %GEMINI_API_KEY%
```

### Linux/Mac
```bash
echo $GEMINI_API_KEY
```

### Test NeoPhysics
```bash
python main.py
```

You should see:
```
[INFO] Running in demo mode - using rule-based parser
```

Instead of the warning message.

## Troubleshooting

1. **Still seeing warning?**
   - Restart your terminal/IDE completely
   - Check that the environment variable is set correctly

2. **Want to use actual Gemini API?**
   - Get free API key from Google AI Studio
   - Replace `demo_mode` with your actual key
   - Restart terminal

3. **Using IDE like VS Code?**
   - Restart VS Code after setting environment variables
   - Or add to VS Code settings.json:
     ```json
     {
       "terminal.integrated.env.windows": {
         "GEMINI_API_KEY": "demo_mode"
       }
     }
     ```

The rule-based parser works great for most commands, so using `demo_mode` is perfectly fine for normal usage!

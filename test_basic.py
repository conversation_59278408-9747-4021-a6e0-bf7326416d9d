#!/usr/bin/env python3
"""
Basic test script for NeoPhysics components.

Tests core functionality without requiring GUI.
"""

import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scene import Scene, Material, MaterialType
from sim.heat3d import Heat3DSolver
from nlp.parser import parse_command

def test_scene_creation():
    """Test basic scene creation and object management."""
    print("Testing scene creation...")
    
    scene = Scene("Test Scene")
    
    # Create objects
    cube_id = scene.create_cube(name="test_cube", position=(1, 0, 0), size=0.5, temperature=100.0)
    sphere_id = scene.create_sphere(name="test_sphere", position=(0, 1, 0), radius=0.3, temperature=50.0)
    
    # Create temperature field
    scene.create_temperature_field(grid_size=(16, 16, 16), initial_temp=20.0)
    
    # Get summary
    summary = scene.get_summary()
    print(f"Scene summary: {summary}")
    
    assert summary['num_objects'] == 2
    assert summary['num_fields'] == 1
    assert 'test_cube' in [obj['name'] for obj in summary['objects']]
    
    print("[OK] Scene creation test passed")

def test_heat_solver():
    """Test 3D heat solver."""
    print("Testing heat solver...")
    
    # Create small grid for testing
    solver = Heat3DSolver(grid_size=(8, 8, 8), grid_spacing=0.1)
    
    # Set initial temperature
    initial_temp = np.full((8, 8, 8), 20.0, dtype=np.float32)
    initial_temp[4, 4, 4] = 100.0  # Hot spot in center
    solver.set_initial_temperature(initial_temp)
    
    # Run a few steps
    for i in range(10):
        dt = solver.step()
        if i == 0:
            print(f"Time step: {dt:.6f}s")
    
    # Check that heat has diffused
    final_temp = solver.get_temperature_field()
    center_temp = final_temp[4, 4, 4]
    neighbor_temp = final_temp[4, 4, 5]
    
    print(f"Center temperature: {center_temp:.2f}°C")
    print(f"Neighbor temperature: {neighbor_temp:.2f}°C")
    
    # Heat should have diffused (center cooled, neighbor warmed)
    assert center_temp < 100.0
    assert neighbor_temp > 20.0
    
    # Get statistics
    stats = solver.get_stats()
    print(f"Temperature stats: {stats}")
    
    print("[OK] Heat solver test passed")

def test_nlp_parser():
    """Test natural language parser."""
    print("Testing NLP parser...")
    
    # Test rule-based parsing (no API key needed)
    commands = [
        "place a cube at the center",
        "add a hot sphere at position 2,1,0",
        "run simulation for 50 steps",
        "create temperature field",
        "clear scene"
    ]
    
    for cmd in commands:
        actions = parse_command(cmd)
        print(f"Command: '{cmd}' -> {len(actions)} actions")
        
        if actions:
            action = actions[0]
            print(f"  Action: {action.type}, Params: {action.parameters}")
        
        assert len(actions) > 0, f"No actions parsed for: {cmd}"
    
    print("[OK] NLP parser test passed")

def test_materials():
    """Test material system."""
    print("Testing materials...")
    
    # Create materials
    copper = Material.thermal("Copper", conductivity=400.0, density=8960.0)
    steel = Material.thermal("Steel", conductivity=50.0, density=7850.0)
    
    assert copper.material_type == MaterialType.THERMAL
    assert copper.properties['thermal_conductivity'] == 400.0
    assert steel.properties['thermal_conductivity'] == 50.0
    
    print("[OK] Materials test passed")

def test_integration():
    """Test integration between components."""
    print("Testing component integration...")
    
    # Create scene with objects
    scene = Scene("Integration Test")
    scene.create_cube(name="hot_cube", position=(0, 0, 0), size=0.2, temperature=100.0)
    scene.create_sphere(name="cold_sphere", position=(1, 0, 0), radius=0.1, temperature=0.0)
    scene.create_temperature_field(grid_size=(10, 10, 10), grid_spacing=0.1)
    
    # Setup heat solver
    temp_field = scene.get_field("temperature")
    solver = Heat3DSolver(
        grid_size=temp_field.shape,
        grid_spacing=temp_field.grid_spacing
    )
    
    # Add objects as boundary conditions
    for obj in scene.objects.values():
        pos = obj.transform.position
        temp = obj.properties.get("temperature", 20.0)
        
        if obj.object_type.value == "cube":
            size = obj.transform.scale[0]
            solver.add_object_boundary(tuple(pos), size, temp, "cube")
        elif obj.object_type.value == "sphere":
            radius = obj.properties.get("radius", 0.1)
            solver.add_object_boundary(tuple(pos), radius*2, temp, "sphere")
    
    # Run simulation
    solver.set_initial_temperature(temp_field.data)
    for i in range(5):
        solver.step()
    
    # Update field data
    temp_field.data = solver.get_temperature_field()
    
    print(f"Final temperature range: {temp_field.data.min():.1f} - {temp_field.data.max():.1f}°C")
    
    print("[OK] Integration test passed")

def main():
    """Run all tests."""
    print("Running NeoPhysics basic tests...\n")
    
    try:
        test_scene_creation()
        print()
        
        test_heat_solver()
        print()
        
        test_nlp_parser()
        print()
        
        test_materials()
        print()
        
        test_integration()
        print()
        
        print("[PARTY] All tests passed!")
        
    except Exception as e:
        print(f"[FAIL] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
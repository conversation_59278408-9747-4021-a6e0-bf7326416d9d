"""
3D Viewport for NeoPhysics using PyVista.

Provides interactive 3D visualization of the scene with objects and fields.
"""

import numpy as np
from typing import Optional, Dict, Any
import pyvista as pv
from pyvistaqt import QtInteractor
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QSlider
from PyQt6.QtCore import Qt

from core.scene import Scene, SceneObject, FieldData, ObjectType

class Viewport3D(QWidget):
    """3D viewport widget using PyVista."""
    
    def __init__(self, scene: Scene):
        super().__init__()
        self.scene = scene
        self.plotter: Optional[QtInteractor] = None
        self.field_actors: Dict[str, Any] = {}
        self.object_actors: Dict[str, Any] = {}
        self.current_field_slice = 0

        # UI state variables
        self.wireframe_mode = False
        self.grid_visible = True
        self.axes_visible = True

        self.setup_ui()
    
    def setup_ui(self):
        """Setup the viewport UI."""
        layout = QVBoxLayout(self)
        
        # Create PyVista plotter
        self.plotter = QtInteractor(self)
        layout.addWidget(self.plotter.interactor)
        
        # Control panel
        controls_layout = QHBoxLayout()
        
        # Field visualization controls
        self.field_label = QLabel("Field Slice:")
        controls_layout.addWidget(self.field_label)
        
        self.slice_slider = QSlider(Qt.Orientation.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(31)  # Default for 32^3 grid
        self.slice_slider.setValue(16)  # Middle slice
        self.slice_slider.valueChanged.connect(self.update_field_slice)
        controls_layout.addWidget(self.slice_slider)
        
        # View controls
        reset_view_btn = QPushButton("Reset View")
        reset_view_btn.clicked.connect(self.reset_view)
        controls_layout.addWidget(reset_view_btn)

        # Camera preset buttons
        front_view_btn = QPushButton("Front")
        front_view_btn.clicked.connect(self.front_view)
        controls_layout.addWidget(front_view_btn)

        top_view_btn = QPushButton("Top")
        top_view_btn.clicked.connect(self.top_view)
        controls_layout.addWidget(top_view_btn)

        side_view_btn = QPushButton("Side")
        side_view_btn.clicked.connect(self.side_view)
        controls_layout.addWidget(side_view_btn)

        iso_view_btn = QPushButton("Isometric")
        iso_view_btn.clicked.connect(self.isometric_view)
        controls_layout.addWidget(iso_view_btn)

        wireframe_btn = QPushButton("Toggle Wireframe")
        wireframe_btn.clicked.connect(self.toggle_wireframe)
        controls_layout.addWidget(wireframe_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # Second row of controls for advanced features
        advanced_controls_layout = QHBoxLayout()

        # Camera controls
        fit_view_btn = QPushButton("Fit All")
        fit_view_btn.clicked.connect(self.fit_view)
        advanced_controls_layout.addWidget(fit_view_btn)

        # Projection controls
        ortho_btn = QPushButton("Orthographic")
        ortho_btn.clicked.connect(self.toggle_projection)
        advanced_controls_layout.addWidget(ortho_btn)

        # Grid toggle
        grid_btn = QPushButton("Toggle Grid")
        grid_btn.clicked.connect(self.toggle_grid)
        advanced_controls_layout.addWidget(grid_btn)

        # Axes toggle
        axes_btn = QPushButton("Toggle Axes")
        axes_btn.clicked.connect(self.toggle_axes)
        advanced_controls_layout.addWidget(axes_btn)

        advanced_controls_layout.addStretch()
        layout.addLayout(advanced_controls_layout)
        
        # Initialize plotter
        self.setup_plotter()
    
    def setup_plotter(self):
        """Setup the PyVista plotter."""
        if not self.plotter:
            return

        # Set background
        self.plotter.set_background('white')

        # Add coordinate axes
        self.plotter.add_axes()

        # Set initial camera position (front view, not slanted)
        self.plotter.camera_position = [(0, -8, 0), (0, 0, 0), (0, 0, 1)]

        # Enable anti-aliasing
        self.plotter.enable_anti_aliasing()

        # Enable better camera controls
        self.plotter.enable_trackball_style()

        # Add grid for better spatial reference
        self.plotter.show_grid()
    
    def refresh(self):
        """Refresh the viewport with current scene data."""
        if not self.plotter:
            return
        
        # Update existing object positions (for real-time physics)
        for obj in self.scene.objects.values():
            if obj.id in self.object_actors:
                actor_info = self.object_actors[obj.id]
                if isinstance(actor_info, dict) and 'mesh' in actor_info:
                    # Update mesh position
                    mesh = actor_info['mesh']
                    mesh.translate(obj.transform.position - mesh.center, inplace=True)
                else:
                    # Remove and re-add object
                    self.plotter.remove_actor(actor_info)
                    self.add_object(obj)
            else:
                # Add new object
                self.add_object(obj)
        
        # Remove objects that no longer exist
        existing_ids = set(obj.id for obj in self.scene.objects.values())
        for obj_id in list(self.object_actors.keys()):
            if obj_id not in existing_ids:
                actor_info = self.object_actors[obj_id]
                if isinstance(actor_info, dict):
                    self.plotter.remove_actor(actor_info['actor'])
                else:
                    self.plotter.remove_actor(actor_info)
                del self.object_actors[obj_id]
        
        # Add fields
        for field in self.scene.fields.values():
            if field.name not in self.field_actors:
                self.add_field(field)
        
        # Update slider range for fields
        self.update_slider_range()
        
        # Add ground plane if not present
        self.add_ground_plane()
        
        # Render
        self.plotter.render()
    
    def clear_actors(self):
        """Clear all actors from the plotter."""
        self.plotter.clear()
        self.field_actors.clear()
        self.object_actors.clear()
        
        # Re-add coordinate axes
        self.plotter.add_axes()
    
    def add_object(self, obj: SceneObject):
        """Add a scene object to the viewport."""
        pos = obj.transform.position
        scale = obj.transform.scale
        
        # Create mesh based on object type
        if obj.object_type == ObjectType.CUBE:
            mesh = pv.Cube(center=pos, x_length=scale[0], y_length=scale[1], z_length=scale[2])
        elif obj.object_type == ObjectType.SPHERE:
            radius = obj.properties.get('radius', scale[0])
            mesh = pv.Sphere(center=pos, radius=radius)
        elif obj.object_type == ObjectType.PLANE:
            mesh = pv.Plane(center=pos, i_size=scale[0], j_size=scale[1])
        else:
            # Default to cube
            mesh = pv.Cube(center=pos, x_length=scale[0], y_length=scale[1], z_length=scale[2])
        
        # Set color based on temperature or physics properties
        temperature = obj.properties.get('temperature', 20.0)
        color = self.temperature_to_color(temperature)
        
        # Physics objects get different styling
        if 'ball' in obj.name.lower() or 'physics' in obj.name.lower():
            color = 'red'
            opacity = 0.9
        else:
            opacity = 0.8
        
        # Add to plotter
        actor = self.plotter.add_mesh(
            mesh, 
            color=color, 
            opacity=opacity,
            show_edges=True,
            edge_color='black',
            line_width=1
        )
        
        self.object_actors[obj.id] = {'actor': actor, 'mesh': mesh}
    
    def add_field(self, field: FieldData):
        """Add a field visualization to the viewport."""
        if field.data.ndim != 3:
            return  # Only support 3D scalar fields for now
        
        # Create structured grid
        nx, ny, nz = field.shape
        origin = field.origin
        spacing = field.grid_spacing
        
        # Create grid
        grid = pv.ImageData(
            dimensions=(nx, ny, nz),
            origin=origin,
            spacing=(spacing, spacing, spacing)
        )
        
        # Add field data
        grid.point_data[field.name] = field.data.flatten(order='F')
        
        # Create slice through the middle
        slice_z = self.current_field_slice
        if slice_z >= nz:
            slice_z = nz // 2
        
        # Create slice
        slice_mesh = grid.slice(normal='z', origin=(0, 0, slice_z * spacing))
        
        # Add to plotter with colormap
        actor = self.plotter.add_mesh(
            slice_mesh,
            scalars=field.name,
            cmap='coolwarm',
            opacity=0.7,
            show_scalar_bar=True,
            scalar_bar_args={'title': f'{field.name} ({field.units})'}
        )
        
        self.field_actors[field.name] = {
            'actor': actor,
            'grid': grid,
            'slice_z': slice_z
        }
    
    def update_field_slice(self, slice_index: int):
        """Update the field slice visualization."""
        self.current_field_slice = slice_index
        
        # Update all field visualizations
        for field_name, field_info in self.field_actors.items():
            field = self.scene.get_field(field_name)
            if field is None:
                continue
            
            # Remove old actor
            self.plotter.remove_actor(field_info['actor'])
            
            # Create new slice
            spacing = field.grid_spacing
            slice_mesh = field_info['grid'].slice(
                normal='z', 
                origin=(0, 0, slice_index * spacing)
            )
            
            # Add new actor
            actor = self.plotter.add_mesh(
                slice_mesh,
                scalars=field.name,
                cmap='coolwarm',
                opacity=0.7,
                show_scalar_bar=True,
                scalar_bar_args={'title': f'{field.name} ({field.units})'}
            )
            
            field_info['actor'] = actor
            field_info['slice_z'] = slice_index
        
        self.plotter.render()
    
    def update_slider_range(self):
        """Update slider range based on current fields."""
        max_z = 31  # Default
        
        for field in self.scene.fields.values():
            if field.data.ndim == 3:
                max_z = max(max_z, field.shape[2] - 1)
        
        self.slice_slider.setMaximum(max_z)
        if self.current_field_slice > max_z:
            self.current_field_slice = max_z // 2
            self.slice_slider.setValue(self.current_field_slice)
    
    def temperature_to_color(self, temperature: float) -> str:
        """Convert temperature to color."""
        # Simple temperature to color mapping
        if temperature < 0:
            return 'blue'
        elif temperature < 20:
            return 'cyan'
        elif temperature < 50:
            return 'green'
        elif temperature < 80:
            return 'yellow'
        elif temperature < 100:
            return 'orange'
        else:
            return 'red'
    
    def reset_view(self):
        """Reset the camera view to front view."""
        if self.plotter:
            self.plotter.reset_camera()
            self.plotter.camera_position = [(0, -8, 0), (0, 0, 0), (0, 0, 1)]
            self.plotter.render()

    def front_view(self):
        """Set camera to front view."""
        if self.plotter:
            self.plotter.camera_position = [(0, -8, 0), (0, 0, 0), (0, 0, 1)]
            self.plotter.render()

    def top_view(self):
        """Set camera to top view."""
        if self.plotter:
            self.plotter.camera_position = [(0, 0, 8), (0, 0, 0), (0, 1, 0)]
            self.plotter.render()

    def side_view(self):
        """Set camera to side view."""
        if self.plotter:
            self.plotter.camera_position = [(8, 0, 0), (0, 0, 0), (0, 0, 1)]
            self.plotter.render()

    def isometric_view(self):
        """Set camera to isometric view."""
        if self.plotter:
            # Isometric view with equal angles
            self.plotter.camera_position = [(5.77, -5.77, 5.77), (0, 0, 0), (0, 0, 1)]
            self.plotter.render()
    
    def add_ground_plane(self):
        """Add a ground plane for physics visualization."""
        if not hasattr(self, 'ground_actor'):
            # Create ground plane (horizontal, in XY plane)
            ground = pv.Plane(center=[0, 0, 0], direction=[0, 0, 1], i_size=10, j_size=10)
            self.ground_actor = self.plotter.add_mesh(
                ground,
                color='lightgray',
                opacity=0.3,
                show_edges=True,
                edge_color='gray'
            )
    
    def fit_view(self):
        """Fit all objects in view."""
        if self.plotter:
            self.plotter.reset_camera()
            self.plotter.render()

    def toggle_projection(self):
        """Toggle between perspective and orthographic projection."""
        if self.plotter:
            if self.plotter.camera.GetParallelProjection():
                self.plotter.camera.SetParallelProjection(False)
            else:
                self.plotter.camera.SetParallelProjection(True)
            self.plotter.render()

    def toggle_grid(self):
        """Toggle grid visibility."""
        if self.plotter:
            if hasattr(self, 'grid_visible') and self.grid_visible:
                self.plotter.remove_actor('grid')
                self.grid_visible = False
            else:
                self.plotter.show_grid()
                self.grid_visible = True
            self.plotter.render()

    def toggle_axes(self):
        """Toggle axes visibility."""
        if self.plotter:
            if hasattr(self, 'axes_visible') and self.axes_visible:
                self.plotter.remove_actor('axes')
                self.axes_visible = False
            else:
                self.plotter.add_axes()
                self.axes_visible = True
            self.plotter.render()

    def toggle_wireframe(self):
        """Toggle wireframe mode for all objects."""
        if not hasattr(self, 'wireframe_mode'):
            self.wireframe_mode = False

        self.wireframe_mode = not self.wireframe_mode

        # Update all object actors
        for obj_id, actor_info in self.object_actors.items():
            if isinstance(actor_info, dict) and 'actor' in actor_info:
                actor = actor_info['actor']
                if self.wireframe_mode:
                    actor.GetProperty().SetRepresentationToWireframe()
                else:
                    actor.GetProperty().SetRepresentationToSurface()

        self.plotter.render()
    
    def closeEvent(self, event):
        """Handle widget close event."""
        if self.plotter:
            self.plotter.close()
        super().closeEvent(event)
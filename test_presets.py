#!/usr/bin/env python3
"""
Test preset physics scenarios.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scene import Scene
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def test_presets():
    """Test preset physics scenarios."""
    print("Testing preset physics scenarios...")
    
    scene = Scene("Presets Test")
    executor = ActionExecutor(scene)
    
    # Test preset commands
    preset_commands = [
        "create a tower of height 5",
        "build a domino run with 10 dominoes",
        "create newton's cradle",
        "spawn 8 spheres with random velocities"
    ]
    
    for cmd in preset_commands:
        print(f"\nTesting: '{cmd}'")
        actions = parse_command(cmd)
        
        if not actions:
            print(f"  [FAIL] No actions parsed")
            continue
        
        for action in actions:
            print(f"  Action: {action.type}")
            
            result = executor.execute(action)
            if result["success"]:
                print(f"  [OK] {result['message']}")
            else:
                print(f"  [FAIL] {result['error']}")
    
    # Print scene summary
    print(f"\nFinal scene summary:")
    summary = scene.get_summary()
    print(f"  Objects: {summary['num_objects']}")
    print(f"  Fields: {summary['num_fields']}")
    
    if executor.rigid_solver:
        print(f"  Rigid bodies: {len(executor.rigid_solver.bodies)}")
    
    # Test physics simulation
    print(f"\nTesting physics simulation...")
    physics_actions = parse_command("run physics for 5 steps")
    for action in physics_actions:
        result = executor.execute(action)
        if result["success"]:
            print(f"  [OK] {result['message']}")
        else:
            print(f"  [FAIL] {result['error']}")
    
    # Step physics a few times
    if executor.rigid_solver:
        print(f"\nStepping physics simulation...")
        for i in range(3):
            result = executor.step_physics(1.0/60.0)
            if result["success"]:
                stats = result.get("stats", {})
                print(f"  Step {i+1}: {stats.get('num_bodies', 0)} bodies, Energy: {stats.get('total_energy', 0):.2f}J")

if __name__ == "__main__":
    test_presets()
    print("\n[PARTY] Preset scenarios test complete!")

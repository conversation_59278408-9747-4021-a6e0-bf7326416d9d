#!/usr/bin/env python3
"""
Test generative scene creation features.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scene import Scene
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def test_generative_commands():
    """Test generative scene creation commands."""
    print("Testing generative scene creation...")
    
    scene = Scene("Generative Test")
    executor = ActionExecutor(scene)
    
    # Test commands
    commands = [
        "spawn 5 spheres with random velocities",
        "create a tower of height 3",
        "build a domino run with 8 dominoes",
        "create newton's cradle",
        "drop 10 cubes in a box with random radii between 0.1 and 0.3"
    ]
    
    for cmd in commands:
        print(f"\nTesting: '{cmd}'")
        actions = parse_command(cmd)
        
        if not actions:
            print(f"  [FAIL] No actions parsed")
            continue
        
        for action in actions:
            print(f"  Action: {action.type}")
            print(f"  Params: {action.parameters}")
            
            result = executor.execute(action)
            if result["success"]:
                print(f"  [OK] {result['message']}")
            else:
                print(f"  [FAIL] {result['error']}")
    
    # Print scene summary
    print(f"\nFinal scene summary:")
    summary = scene.get_summary()
    print(f"  Objects: {summary['num_objects']}")
    print(f"  Fields: {summary['num_fields']}")
    
    # Test physics simulation
    print(f"\nTesting physics simulation...")
    physics_actions = parse_command("run physics for 10 steps")
    for action in physics_actions:
        result = executor.execute(action)
        if result["success"]:
            print(f"  [OK] {result['message']}")
        else:
            print(f"  [FAIL] {result['error']}")

if __name__ == "__main__":
    test_generative_commands()
    print("\n[PARTY] Generative features test complete!")

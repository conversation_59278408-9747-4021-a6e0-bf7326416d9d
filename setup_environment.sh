#!/bin/bash
# <PERSON><PERSON>t to permanently set GEMINI_API_KEY environment variable

echo "Setting up NeoPhysics environment..."

# Detect shell
if [ -n "$ZSH_VERSION" ]; then
    SHELL_RC="$HOME/.zshrc"
elif [ -n "$BASH_VERSION" ]; then
    SHELL_RC="$HOME/.bashrc"
else
    SHELL_RC="$HOME/.profile"
fi

echo "Detected shell config: $SHELL_RC"

# Check if already set
if grep -q "GEMINI_API_KEY" "$SHELL_RC"; then
    echo "GEMINI_API_KEY already exists in $SHELL_RC"
    echo "Please edit manually if you want to change it."
else
    # Add to shell config
    echo "" >> "$SHELL_RC"
    echo "# NeoPhysics Gemini API Key" >> "$SHELL_RC"
    echo "export GEMINI_API_KEY=\"demo_mode\"" >> "$SHELL_RC"
    echo "GEMINI_API_KEY added to $SHELL_RC"
fi

echo ""
echo "Environment variable set successfully!"
echo "Please restart your terminal or run: source $SHELL_RC"
echo ""
echo "To verify, run: echo \$GEMINI_API_KEY"
echo ""
echo "To use actual Gemini API:"
echo "1. Get API key from: https://makersuite.google.com/app/apikey"
echo "2. Edit $SHELL_RC and replace 'demo_mode' with your actual key"
echo "3. Run: source $SHELL_RC"

# Make executable
chmod +x "$0"

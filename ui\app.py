"""
Main Application for NeoPhysics - A Blender-like 3D Physics Sandbox.

Provides the main window, menu system, and coordinates all UI components.
"""

import sys
import os
from typing import Optional

# Import config first to load .env file
import config
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QSplitter, QMenuBar, QMenu, QStatusBar,
                            QMessageBox, QFileDialog, QInputDialog)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QKeySequence

from .viewport import Viewport3D
from .command_panel import CommandPanel
from .scene_inspector import SceneInspector
from .timeline import Timeline
from .physics_controls import PhysicsControls
from core.scene import Scene
from nlp.parser import parse_command, Action
from runtime.action_executor import ActionExecutor

class NeoPhysicsApp:
    """Main application class for NeoPhysics."""
    
    def __init__(self):
        """Initialize the application."""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("NeoPhysics")
        self.app.setApplicationVersion("0.1.0")
        
        # Core components
        self.scene = Scene("New Scene")
        self.executor = ActionExecutor(self.scene)
        
        # Create main window
        self.main_window = MainWindow(self.scene)
        self.main_window.parent = lambda: self  # Allow access to app from main window
        
        # Connect signals
        self.main_window.command_executed.connect(self.handle_command)
        self.main_window.simulation_requested.connect(self.run_simulation)
        self.main_window.physics_step_requested.connect(self.step_physics)
        
        # Setup Gemini API key
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not self.gemini_api_key or self.gemini_api_key == 'demo_mode':
            # Only show warning if no key is set at all
            if not self.gemini_api_key:
                print("Warning: GEMINI_API_KEY not found. Using rule-based parser only.")
    
    def handle_command(self, command: str):
        """Handle a natural language command."""
        try:
            actions = parse_command(command, self.gemini_api_key)
            
            for action in actions:
                result = self.executor.execute(action)
                if not result["success"]:
                    self.main_window.show_error(result["error"])
                    return
                else:
                    self.main_window.show_info(result["message"])
            
            # Update UI
            self.main_window.refresh_scene()
            
        except Exception as e:
            self.main_window.show_error(f"Command failed: {str(e)}")
    

    
    def run_simulation(self, steps: int = 100, duration: Optional[float] = None):
        """Run physics simulation."""
        from nlp.parser import Action
        
        params = {"steps": steps}
        if duration is not None:
            params["duration"] = duration
        
        action = Action("run_simulation", params)
        result = self.executor.execute(action)
        
        if result["success"]:
            self.main_window.show_info(result["message"])
            self.main_window.refresh_scene()
        else:
            self.main_window.show_error(result["error"])
    
    def step_physics(self, dt: Optional[float] = None):
        """Step physics simulation once."""
        result = self.executor.step_physics(dt)
        if result["success"]:
            self.main_window.update_physics_stats(result.get("stats", {}))
            self.main_window.refresh_scene()

            # Record frame if recording is enabled
            if hasattr(self.main_window, 'timeline'):
                current_time = result.get("stats", {}).get("time", 0.0)
                self.main_window.timeline.record_frame(
                    current_time,
                    self.executor.get_rigid_solver(),
                    self.scene
                )
    
    def set_gravity(self, gravity: float):
        """Set gravity for physics simulation."""
        self.executor.set_gravity(gravity)
    

    
    def run(self) -> int:
        """Run the application."""
        self.main_window.show()
        return self.app.exec()

class MainWindow(QMainWindow):
    """Main window for NeoPhysics."""
    
    command_executed = pyqtSignal(str)
    simulation_requested = pyqtSignal(int, object)  # steps, duration
    physics_step_requested = pyqtSignal(float)  # dt
    
    def __init__(self, scene: Scene):
        super().__init__()
        self.scene = scene
        self.setup_ui()
        self.setup_menus()
        self.setup_status_bar()
    
    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("NeoPhysics - 3D Physics Sandbox")
        self.setGeometry(100, 100, 1400, 900)
        
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel (Scene Inspector)
        self.scene_inspector = SceneInspector(self.scene)
        splitter.addWidget(self.scene_inspector)
        
        # Center panel (3D Viewport)
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        
        self.viewport = Viewport3D(self.scene)
        center_layout.addWidget(self.viewport)
        
        # Timeline at bottom of center panel
        self.timeline = Timeline()
        self.timeline.replay_frame_requested.connect(self.replay_frame)
        center_layout.addWidget(self.timeline)
        
        splitter.addWidget(center_widget)
        
        # Right panel with tabs
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Command Panel
        self.command_panel = CommandPanel()
        self.command_panel.command_submitted.connect(self.command_executed.emit)
        right_layout.addWidget(self.command_panel)
        
        # Physics Controls
        self.physics_controls = PhysicsControls()
        self.physics_controls.gravity_changed.connect(self.on_gravity_changed)
        self.physics_controls.simulation_started.connect(self.on_physics_started)
        self.physics_controls.simulation_stopped.connect(self.on_physics_stopped)
        self.physics_controls.simulation_reset.connect(self.on_physics_reset)
        right_layout.addWidget(self.physics_controls)
        
        splitter.addWidget(right_widget)
        
        # Set splitter proportions
        splitter.setSizes([250, 800, 350])
    
    def setup_menus(self):
        """Setup menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        new_action = QAction("New Scene", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.new_scene)
        file_menu.addAction(new_action)
        
        open_action = QAction("Open Scene", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_scene)
        file_menu.addAction(open_action)
        
        save_action = QAction("Save Scene", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_scene)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Simulation menu
        sim_menu = menubar.addMenu("Simulation")
        
        run_action = QAction("Run Simulation", self)
        run_action.setShortcut(QKeySequence("Space"))
        run_action.triggered.connect(self.run_simulation_dialog)
        sim_menu.addAction(run_action)
        
        reset_action = QAction("Reset Simulation", self)
        reset_action.triggered.connect(self.reset_simulation)
        sim_menu.addAction(reset_action)
        
        # View menu
        view_menu = menubar.addMenu("View")
        
        reset_view_action = QAction("Reset View", self)
        reset_view_action.setShortcut(QKeySequence("Home"))
        reset_view_action.triggered.connect(self.viewport.reset_view)
        view_menu.addAction(reset_view_action)
        
        # Help menu
        help_menu = menubar.addMenu("Help")
        
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """Setup status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def new_scene(self):
        """Create a new scene."""
        self.scene.clear()
        self.refresh_scene()
        self.status_bar.showMessage("New scene created")
    
    def open_scene(self):
        """Open a scene file."""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open Scene", "", "JSON Files (*.json);;All Files (*)"
        )
        if filename:
            # TODO: Implement scene loading
            self.status_bar.showMessage(f"Scene loading not yet implemented: {filename}")
    
    def save_scene(self):
        """Save the current scene."""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Scene", "", "JSON Files (*.json);;All Files (*)"
        )
        if filename:
            # TODO: Implement scene saving
            self.status_bar.showMessage(f"Scene saving not yet implemented: {filename}")
    
    def run_simulation_dialog(self):
        """Show simulation parameters dialog."""
        steps, ok = QInputDialog.getInt(
            self, "Run Simulation", "Number of steps:", 100, 1, 10000
        )
        if ok:
            self.simulation_requested.emit(steps, None)
    
    def reset_simulation(self):
        """Reset simulation state."""
        # TODO: Implement simulation reset
        self.status_bar.showMessage("Simulation reset")
    
    def show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self, "About NeoPhysics",
            "NeoPhysics v0.1.0\n\n"
            "A Blender-like 3D Physics Sandbox with Natural Language Interface\n\n"
            "Built with PyQt6, PyVista, and PyTorch"
        )
    
    def refresh_scene(self):
        """Refresh all UI components with current scene data."""
        self.scene_inspector.refresh()
        self.viewport.refresh()
        self.update_status()
    
    def update_status(self):
        """Update status bar with scene information."""
        summary = self.scene.get_summary()
        status = f"Objects: {summary['num_objects']} | Fields: {summary['num_fields']} | Time: {summary['time']:.2f}s"
        self.status_bar.showMessage(status)
    
    def update_progress(self, current: int, total: int):
        """Update progress during simulation."""
        progress = int(100 * current / total)
        self.status_bar.showMessage(f"Simulation progress: {progress}% ({current}/{total})")
    
    def show_error(self, message: str):
        """Show error message."""
        QMessageBox.critical(self, "Error", message)
        self.status_bar.showMessage("Error occurred")
    
    def show_info(self, message: str):
        """Show information message."""
        QMessageBox.information(self, "Information", message)
        self.status_bar.showMessage("Ready")
    
    def on_gravity_changed(self, gravity: float):
        """Handle gravity change from physics controls."""
        # Get the app instance and set gravity
        app = self.parent()
        if hasattr(app, 'set_gravity'):
            app.set_gravity(gravity)
    
    def on_physics_started(self):
        """Handle physics simulation start."""
        # Connect timer to physics stepping
        if not hasattr(self, 'physics_timer'):
            self.physics_timer = QTimer()
            self.physics_timer.timeout.connect(lambda: self.physics_step_requested.emit(self.physics_controls.get_timestep()))
        
        fps = int(1.0 / self.physics_controls.get_timestep())
        self.physics_timer.start(1000 // fps)
    
    def on_physics_stopped(self):
        """Handle physics simulation stop."""
        if hasattr(self, 'physics_timer'):
            self.physics_timer.stop()
    
    def on_physics_reset(self):
        """Handle physics simulation reset."""
        # Reset physics simulation
        pass
    
    def update_physics_stats(self, stats: dict):
        """Update physics statistics display."""
        self.physics_controls.update_stats(stats)

    def replay_frame(self, frame):
        """Replay a simulation frame."""
        from core.simulation_recorder import SimulationFrame

        if not isinstance(frame, SimulationFrame):
            return

        # Update rigid body positions from recorded frame
        app = self.parent()
        if hasattr(app, 'executor') and app.executor.rigid_solver:
            rigid_solver = app.executor.rigid_solver

            for body_id, body_data in frame.bodies.items():
                if body_id in rigid_solver.bodies:
                    body = rigid_solver.bodies[body_id]
                    body.position = np.array(body_data['position'])
                    body.velocity = np.array(body_data['velocity'])
                    body.rotation = np.array(body_data['rotation'])
                    body.angular_velocity = np.array(body_data['angular_velocity'])

                    # Update scene object position
                    scene_obj = self.scene.get_object(body_id)
                    if scene_obj:
                        scene_obj.transform.position = body.position.copy()

        # Update timeline display
        self.timeline.set_time(frame.time)

        # Update physics stats display
        self.update_physics_stats(frame.stats)

        # Refresh viewport
        self.refresh_scene()
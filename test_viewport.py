#!/usr/bin/env python3
"""
Test script to verify the 3D viewport camera positioning and controls.
"""

import sys
from PyQt6.QtWidgets import QApplication
from core.scene import Scene
from ui.viewport import Viewport3D

def test_viewport():
    """Test the 3D viewport with some basic objects."""
    app = QApplication(sys.argv)
    
    # Create a test scene
    scene = Scene("Test Scene")
    
    # Add some test objects
    scene.create_cube(name="Test Cube", position=(0, 0, 0), size=1.0, temperature=50.0)
    scene.create_sphere(name="Test Sphere", position=(2, 0, 0), radius=0.5, temperature=80.0)
    scene.create_cube(name="Hot Cube", position=(-2, 0, 0), size=0.8, temperature=100.0)
    
    # Create the viewport
    viewport = Viewport3D(scene)
    viewport.setWindowTitle("NeoPhysics 3D Viewport Test")
    viewport.resize(800, 600)
    viewport.show()
    
    # Refresh to display objects
    viewport.refresh()
    
    print("3D Viewport Test Started")
    print("Camera should now be positioned at front view (not slanted)")
    print("Available controls:")
    print("- Reset View: Returns to front view")
    print("- Front: Front view")
    print("- Top: Top-down view")
    print("- Side: Side view")
    print("- Isometric: Isometric view")
    print("- Fit All: Fit all objects in view")
    print("- Orthographic: Toggle perspective/orthographic projection")
    print("- Toggle Grid: Show/hide grid")
    print("- Toggle Axes: Show/hide coordinate axes")
    print("- Toggle Wireframe: Switch between solid and wireframe rendering")
    print("\nMouse controls:")
    print("- Left click + drag: Rotate view")
    print("- Right click + drag: Pan view")
    print("- Scroll wheel: Zoom in/out")
    
    return app.exec()

if __name__ == "__main__":
    test_viewport()

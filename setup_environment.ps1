# PowerShell script to permanently set GEMINI_API_KEY environment variable
# Run this as Administrator for system-wide setting

Write-Host "Setting up NeoPhysics environment..." -ForegroundColor Green

# Option 1: Set for current user only (recommended)
Write-Host "Setting GEMINI_API_KEY for current user..." -ForegroundColor Yellow
[Environment]::SetEnvironmentVariable("GEMINI_API_KEY", "demo_mode", "User")

# Option 2: Uncomment below to set system-wide (requires admin)
# Write-Host "Setting GEMINI_API_KEY system-wide..." -ForegroundColor Yellow
# [Environment]::SetEnvironmentVariable("GEMINI_API_KEY", "demo_mode", "Machine")

Write-Host "Environment variable set successfully!" -ForegroundColor Green
Write-Host "Please restart your terminal/IDE for changes to take effect." -ForegroundColor Cyan
Write-Host ""
Write-Host "To verify, run: Get-ChildItem Env:GEMINI_API_KEY" -ForegroundColor Gray

# If you have an actual Gemini API key, replace "demo_mode" with your key:
Write-Host ""
Write-Host "To use actual Gemini API:" -ForegroundColor Magenta
Write-Host "1. Get API key from: https://makersuite.google.com/app/apikey" -ForegroundColor Gray
Write-Host "2. Replace 'demo_mode' with your actual key in this script" -ForegroundColor Gray
Write-Host "3. Run this script again" -ForegroundColor Gray
